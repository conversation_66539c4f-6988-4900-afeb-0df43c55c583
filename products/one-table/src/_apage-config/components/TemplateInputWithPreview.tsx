import { FC, useState } from 'react';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Popover } from '@metroDesign/popover';
import { Spin } from '@metroDesign/spin';
import { If } from '@mdtBsComm/components/if';
import { queryPeriodFormNameAsync } from '@mdtBsServices/flowork';
import VariableInputFormily from '@mdtProMicroModules/components/variable-input-formily';
import i18n from '../../languages';

export const TemplateInputWithPreview: FC<any> = (props) => {
  const { value, nameValue } = props;
  const [renderPreviewName, setRenderPreviewName] = useState<string | undefined>(undefined);
  const [renderLoading, setRenderLoading] = useState(false);
  const renderPreview = () => {
    return renderLoading ? <Spin /> : <div>{nameValue + renderPreviewName}</div>;
  };

  return (
    <Flex vertical gap={6}>
      <VariableInputFormily {...props} />
      {value ? (
        <Flex justify="flex-start">
          <If
            data={!!nameValue}
            else={
              <Button.Link size="small" primary disabled>
                {i18n.chain.proMicroModules.oneTable.viewPeriodExample}
              </Button.Link>
            }
          >
            <Popover
              overlayStyle={{ maxWidth: 400 }}
              placement="leftBottom"
              title={i18n.chain.proMicroModules.oneTable.viewPeriodExample}
              content={renderPreview}
              onOpenChange={async (visible) => {
                if (visible) {
                  setRenderLoading(true);
                  const resp = await queryPeriodFormNameAsync({
                    template: value,
                    form: {
                      name: nameValue,
                      extra_meta: {},
                    },
                  });
                  setRenderPreviewName(resp?.data?.result);

                  setRenderLoading(false);
                }
              }}
              trigger="click"
            >
              <Button.Link size="small" primary>
                {i18n.chain.proMicroModules.oneTable.viewPeriodExample}
              </Button.Link>
            </Popover>
          </If>
        </Flex>
      ) : null}
    </Flex>
  );
};

import {
  batchQueryWorkflowGenealogy,
  checkAllowInitiate,
  deleteFloworkForm,
  deleteWorkflow,
  deleteWorkflowSpec,
  disableWorkflowSpec,
  enableWorkflowSpec,
  excuteOnetableCommands,
  getFloworkForm,
  getFloworkTask,
  getWorkflow,
  getWorkflowSpec,
  getWorkflowSpecIns,
  patchFloworkForm,
  post<PERSON>loworkForm,
  postFloworkTaskComplete,
  postQueryFloworkForms,
  postWorkflowCancel,
  postWorkflowSpec,
  postWorkflowSpecFlow,
  postWorkflowSpecFromDatapkg,
  putWorkflowSpec,
  queryFloworkForms,
  queryFloworkServices,
  queryFloworkTasks,
  queryOnetableApprovalTasks,
  queryOnetableAssignTasks,
  queryOnetableDownstreamStat,
  queryOnetableDownstreamUsers,
  queryOnetableGrantedForms,
  queryOnetableInvolvedForms,
  queryOnetableInvolvedUsers,
  queryOnetableManagedForms,
  queryOnetableManageTasks,
  queryPeriodFormName,
  queryWorkflowGenealogy,
  queryWorkflowNodes,
  queryWorkflows,
  queryWorkflowsByPost,
  queryWorkflowSpecFlows,
  queryWorkflowSpecNodes,
  queryWorkflowSpecs,
  queryWorkflowSpecsByPost,
  queryWorkflowSpecsByTask,
  queryWorkflowSpecsByWorkflow,
  queryWorkflowTasks,
} from '@mdtApis/api/flowork';
import { getTotalArgs } from './_util/totalUtil';
import {
  IFloworkForm,
  IFloworkFormPatch,
  IFloworkFormPost,
  IFloworkFormsPostQuery,
  IFloworkFormsQuery,
  IFloworkService,
  IFloworkTask,
  IFloworkTaskCompletePost,
  IFloworkTasksQuery,
  IOnetableApprovalTask,
  IOnetableApprovalTasksQueryPost,
  IOnetableAssignTask,
  IOnetableAssignTasksQueryPost,
  IOnetableDownstreamStat,
  IOnetableDownstreamStatPost,
  IOnetableDownstreamUsers,
  IOnetableDownstreamUsersPost,
  IOnetableExecuteCommandsPost,
  IOnetableExecuteCommandsResult,
  IOnetableGrantedForm,
  IOnetableGrantedFormQueryPost,
  IOnetableInvolvedForm,
  IOnetableInvolvedFormsQueryPost,
  IOnetableInvolvedUsers,
  IOnetableInvolvedUsersQueryPost,
  IOnetableManagedForm,
  IOnetableManagedFormsQueryPost,
  IOnetableManageTask,
  IOnetableManageTasksQueryPost,
  IOnetablePeriodFormNameQueryPost,
  IPaginationQuery,
  IRequestRequestConfig,
  IServerPaginationResponse,
  IServerResponse,
  IWorkflow,
  IWorkflowCancelPost,
  IWorkflowDelete,
  IWorkflowGenealogy,
  IWorkflowGenealogyBatchItem,
  IWorkflowGenealogyBatchPost,
  IWorkflowGenealogyQuery,
  IWorkflowGet,
  IWorkflowNode,
  IWorkflowNodesQuery,
  IWorkflowSpec,
  IWorkflowSpecFlowPost,
  IWorkflowSpecFlowsQuery,
  IWorkflowSpecGet,
  IWorkflowSpecPost,
  IWorkflowSpecPut,
  IWorkflowSpecRef,
  IWorkflowSpecsQuery,
  IWorkflowSpecsQueryPost,
  IWorkflowSpecsRefQuery,
  IWorkflowsQuery,
  IWorkflowsQueryPost,
  IWorkflowTasksQuery,
} from './interfaces';

export const queryFloworkServicesAsync = async (config?: IRequestRequestConfig) => {
  return queryFloworkServices(config) as unknown as IServerResponse<IFloworkService[]>;
};

export const queryFloworkFormsAsync = async (config?: IRequestRequestConfig<IFloworkFormsQuery>) => {
  return queryFloworkForms(config) as unknown as IServerResponse<IFloworkForm[]>;
};

export const queryFloworkFormsPaginationAsync = async (config?: IRequestRequestConfig<IFloworkFormsQuery>) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryFloworkForms(cnf) as unknown as IServerPaginationResponse<IFloworkForm[]>;
};

export const postQueryFloworkFormsAsync = async (
  data: IFloworkFormsPostQuery,
  config?: IRequestRequestConfig<IFloworkFormsQuery>,
) => {
  return postQueryFloworkForms(data, config) as unknown as IServerResponse<IFloworkForm[]>;
};

export const postQueryFloworkFormsPaginationAsync = async (
  data: IFloworkFormsPostQuery,
  config?: IRequestRequestConfig<IFloworkFormsQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return postQueryFloworkForms(data, cnf) as unknown as IServerPaginationResponse<IFloworkForm[]>;
};

export const postFloworkFormAsync = async (data: IFloworkFormPost, config?: IRequestRequestConfig) => {
  return postFloworkForm(data, config) as unknown as IServerResponse<IFloworkForm>;
};

export const getFloworkFormAsync = async (formId: string, config?: IRequestRequestConfig) => {
  return getFloworkForm(formId, config) as unknown as IServerResponse<IFloworkForm>;
};

export const patchFloworkFormAsync = async (
  formId: string,
  data: IFloworkFormPatch,
  config?: IRequestRequestConfig,
) => {
  return patchFloworkForm(formId, data, config) as unknown as IServerResponse<IFloworkForm>;
};

export const deleteFloworkFormAsync = async (formId: string, config?: IRequestRequestConfig) => {
  return deleteFloworkForm(formId, config) as unknown as IServerResponse<Boolean>;
};

export const queryFloworkTasksAsync = async (config?: IRequestRequestConfig<IFloworkTasksQuery>) => {
  return queryFloworkTasks(config) as unknown as IServerResponse<IFloworkTask[]>;
};

export const queryFloworkTasksPaginationAsync = async (config?: IRequestRequestConfig<IFloworkTasksQuery>) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryFloworkTasks(cnf) as unknown as IServerPaginationResponse<IFloworkTask[]>;
};

export const queryFloworkTasksPaginationTotalAsync: typeof queryFloworkTasksPaginationAsync = (...args) => {
  return queryFloworkTasksPaginationAsync(...getTotalArgs(args));
};

export const getFloworkTaskAsync = async (taskId: string, config?: IRequestRequestConfig) => {
  return getFloworkTask(taskId, config) as unknown as IServerResponse<IFloworkTask>;
};

export const postFloworkTaskCompleteAsync = async (
  taskId: string,
  data: IFloworkTaskCompletePost,
  config?: IRequestRequestConfig,
) => {
  return postFloworkTaskComplete(taskId, data, config) as unknown as IServerResponse<IFloworkTask>;
};

export const queryWorkflowsByPostAsync = async (
  data: IWorkflowsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryWorkflowsByPost(data, config) as unknown as IServerResponse<IWorkflow[]>;
};

export const queryWorkflowsPaginationAsync = async (config?: IRequestRequestConfig<IWorkflowsQuery>) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryWorkflows(cnf) as unknown as IServerPaginationResponse<IWorkflow[]>;
};

export const queryWorkflowsPaginationTotalAsync: typeof queryWorkflowsPaginationAsync = (...args) => {
  return queryWorkflowsPaginationAsync(...getTotalArgs(args));
};

export const queryWorkflowsAsync = async (config?: IRequestRequestConfig<IWorkflowsQuery>) => {
  return queryWorkflows(config) as unknown as IServerResponse<IWorkflow[]>;
};

export const getWorkflowAsync = async (workflowId: string, config?: IRequestRequestConfig<IWorkflowGet>) => {
  return getWorkflow(workflowId, config) as unknown as IServerResponse<IWorkflow>;
};

export const deleteWorkflowAsync = (workflowId: string, config?: IRequestRequestConfig<IWorkflowDelete>) => {
  return deleteWorkflow(workflowId, config) as unknown as IServerResponse<Boolean>;
};

export const postWorkflowCancelAsync = async (
  workflowId: string,
  data?: IWorkflowCancelPost,
  config?: IRequestRequestConfig,
) => {
  return postWorkflowCancel(workflowId, data, config) as unknown as IServerResponse<IWorkflow>;
};

export const queryWorkflowNodesAsync = async (
  workflowId: string,
  config?: IRequestRequestConfig<IWorkflowNodesQuery>,
) => {
  return queryWorkflowNodes(workflowId, config) as unknown as IServerResponse<IWorkflowNode[]>;
};

export const queryWorkflowTasksAsync = async (
  workflowId: string,
  config?: IRequestRequestConfig<IWorkflowTasksQuery>,
) => {
  return queryWorkflowTasks(workflowId, config) as unknown as IServerResponse<IFloworkTask[]>;
};

export const getWorkflowSpecInsAsync = async (workflowId: string, config?: IRequestRequestConfig<IWorkflowSpecGet>) => {
  return getWorkflowSpecIns(workflowId, config) as unknown as IServerResponse<IWorkflowSpec>;
};

export const queryWorkflowSpecsAsync = async (config?: IRequestRequestConfig<IWorkflowSpecsQuery>) => {
  return queryWorkflowSpecs(config) as unknown as IServerResponse<IWorkflowSpec[]>;
};

export const queryWorkflowGenealogyAsync = async (
  workflowId: string,
  config?: IRequestRequestConfig<IWorkflowGenealogyQuery>,
) => {
  return queryWorkflowGenealogy(workflowId, config) as unknown as IServerResponse<IWorkflowGenealogy[]>;
};

export const queryWorkflowSpecsByPostAsync = async (
  data: IWorkflowSpecsQueryPost,
  config: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryWorkflowSpecsByPost(data, config) as unknown as IServerResponse<IWorkflowSpec[]>;
};

export const queryWorkflowSpecsPaginationAsync = async (config?: IRequestRequestConfig<IWorkflowSpecsQuery>) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryWorkflowSpecs(cnf) as unknown as IServerPaginationResponse<IWorkflowSpec[]>;
};

export const queryWorkflowSpecsPaginationTotalAsync: typeof queryWorkflowSpecsPaginationAsync = (...args) => {
  return queryWorkflowSpecsPaginationAsync(...getTotalArgs(args));
};

export const postWorkflowSpecAsync = async (data: IWorkflowSpecPost, config?: IRequestRequestConfig) => {
  return postWorkflowSpec(data, config) as unknown as IServerResponse<IWorkflowSpec>;
};

export const postWorkflowSpecFromDatapkgAsync = async (data: IWorkflowSpecPost, config?: IRequestRequestConfig) => {
  return postWorkflowSpecFromDatapkg(data, config) as unknown as IServerResponse<IWorkflowSpec>;
};

export const getWorkflowSpecAsync = async (
  workflowSpecId: string,
  config?: IRequestRequestConfig<IWorkflowSpecGet>,
) => {
  return getWorkflowSpec(workflowSpecId, config) as unknown as IServerResponse<IWorkflowSpec>;
};

export const putWorkflowSpecAsync = async (
  workflowSpecId: string,
  data: IWorkflowSpecPut,
  config?: IRequestRequestConfig,
) => {
  return putWorkflowSpec(workflowSpecId, data, config) as unknown as IServerResponse<IWorkflowSpec>;
};

export const deleteWorkflowSpecAsync = async (workflowSpecId: string, config?: IRequestRequestConfig) => {
  return deleteWorkflowSpec(workflowSpecId, config) as unknown as IServerResponse;
};

export const enableWorkflowSpecAsync = async (workflowSpecId: string, config?: IRequestRequestConfig) => {
  return enableWorkflowSpec(workflowSpecId, config) as unknown as IServerResponse;
};

export const disableWorkflowSpecAsync = async (workflowSpecId: string, config?: IRequestRequestConfig) => {
  return disableWorkflowSpec(workflowSpecId, config) as unknown as IServerResponse;
};

export const queryWorkflowSpecNodesAsync = async (workflowSpecId: string, config?: IRequestRequestConfig) => {
  return queryWorkflowSpecNodes(workflowSpecId, config) as unknown as IServerResponse<IWorkflowNode[]>;
};

export const queryWorkflowSpecFlowsAsync = async (
  workflowSpecId: string,
  config?: IRequestRequestConfig<IWorkflowSpecFlowsQuery>,
) => {
  return queryWorkflowSpecFlows(workflowSpecId, config) as unknown as IServerResponse<IWorkflow[]>;
};

export const queryWorkflowSpecFlowsPaginationAsync = async (
  workflowSpecId: string,
  config?: IRequestRequestConfig<IWorkflowSpecFlowsQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryWorkflowSpecFlows(workflowSpecId, cnf) as unknown as IServerPaginationResponse<IWorkflow[]>;
};

export const queryWorkflowSpecFlowsPaginationTotalAsync: typeof queryWorkflowSpecFlowsPaginationAsync = (...args) => {
  return queryWorkflowSpecFlowsPaginationAsync(...getTotalArgs(args, 1));
};

export const postWorkflowSpecFlowAsync = async (
  workflowSpecId: string,
  data: IWorkflowSpecFlowPost,
  config?: IRequestRequestConfig,
) => {
  return postWorkflowSpecFlow(workflowSpecId, data, config) as unknown as IServerResponse<IWorkflow>;
};

export const queryWorkflowSpecsByTaskAsync = async (config?: IRequestRequestConfig<IWorkflowSpecsRefQuery>) => {
  return queryWorkflowSpecsByTask(config) as unknown as IServerResponse<IWorkflowSpecRef[]>;
};

export const queryWorkflowSpecsByWorkflowAsync = async (config?: IRequestRequestConfig<IWorkflowSpecsRefQuery>) => {
  return queryWorkflowSpecsByWorkflow(config) as unknown as IServerResponse<IWorkflowSpecRef[]>;
};

export const checkAllowInitiateAsync = async (workflowSpecId: string, config?: IRequestRequestConfig) => {
  return checkAllowInitiate(workflowSpecId, config) as unknown as IServerResponse<boolean>;
};

export const excuteOnetableCommandsAsync = async (
  data: IOnetableExecuteCommandsPost,
  config?: IRequestRequestConfig,
) => {
  return excuteOnetableCommands(data, config) as unknown as IServerResponse<IOnetableExecuteCommandsResult>;
};

export const queryOnetableDownstreamUsersAsync = async (
  data: IOnetableDownstreamUsersPost,
  config?: IRequestRequestConfig,
) => {
  return queryOnetableDownstreamUsers(data, config) as unknown as IServerResponse<IOnetableDownstreamUsers>;
};

export const queryOnetableDownstreamStatAsync = async (
  data: IOnetableDownstreamStatPost,
  config?: IRequestRequestConfig,
) => {
  return queryOnetableDownstreamStat(data, config) as unknown as IServerResponse<IOnetableDownstreamStat[]>;
};

export const queryOnetableInvolvedFormsAsync = async (
  data: IOnetableInvolvedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableInvolvedForms(data, config) as unknown as IServerResponse<IOnetableInvolvedForm[]>;
};

export const queryOnetableInvolvedUsersAsync = async (
  data: IOnetableInvolvedUsersQueryPost,
  config?: IRequestRequestConfig,
) => {
  return queryOnetableInvolvedUsers(data, config) as unknown as IServerResponse<IOnetableInvolvedUsers>;
};

export const queryOnetableInvolvedFormsPaginationAsync = async (
  data: IOnetableInvolvedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryOnetableInvolvedForms(data, cnf) as unknown as IServerPaginationResponse<IOnetableInvolvedForm[]>;
};

export const queryOnetableManagedFormsAsync = async (
  data: IOnetableManagedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableManagedForms(data, config) as unknown as IServerResponse<IOnetableManagedForm[]>;
};

export const queryOnetableGrantedFormsPaginationAsync = async (
  data: IOnetableGrantedFormQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryOnetableGrantedForms(data, cnf) as unknown as IServerPaginationResponse<IOnetableGrantedForm[]>;
};

export const queryOnetableGrantedFormsAsync = async (
  data: IOnetableGrantedFormQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableGrantedForms(data, config) as unknown as IServerResponse<IOnetableGrantedForm[]>;
};

export const queryOnetableAssignTasksAsync = async (
  data: IOnetableAssignTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableAssignTasks(data, config) as unknown as IServerResponse<IOnetableAssignTask[]>;
};

export const queryOnetableManageTasksAsync = async (
  data: IOnetableManageTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableManageTasks(data, config) as unknown as IServerResponse<IOnetableManageTask[]>;
};

export const queryOnetableApprovalTasksAsync = async (
  data: IOnetableApprovalTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return queryOnetableApprovalTasks(data, config) as unknown as IServerResponse<IOnetableApprovalTask[]>;
};

export const queryOnetableManagedFormsPaginationAsync = async (
  data: IOnetableManagedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryOnetableManagedForms(data, cnf) as unknown as IServerPaginationResponse<IOnetableManagedForm[]>;
};

export const batchQueryWorkflowGenealogyAsync = async (
  data: IWorkflowGenealogyBatchPost,
  config?: IRequestRequestConfig,
) => {
  return batchQueryWorkflowGenealogy(data, config) as unknown as IServerResponse<IWorkflowGenealogyBatchItem[]>;
};

export const queryPeriodFormNameAsync = async (
  data: IOnetablePeriodFormNameQueryPost,
  config?: IRequestRequestConfig,
) => {
  return queryPeriodFormName(data, config) as unknown as IServerResponse<{ result: string }>;
};
